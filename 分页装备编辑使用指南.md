# 分页装备编辑使用指南

## 🎯 新增功能

现在装备重铸界面的编辑功能已经升级为7页菜单，包含了所有扩展属性支持！

## 📋 菜单结构

当你在装备重铸界面点击"编辑当前数据"时，会看到：

```
装备编辑菜单

【1】基础属性编辑
【2】技能管理编辑  
【3】时间设置编辑
【4】掉落物品编辑
【5】掉率属性编辑
【6】经验好感编辑
【7】战斗属性编辑

请输入页面编号(1-7)或直接输入指令：
```

## 🎮 使用方法

### 方法1：分页操作
1. 输入页面编号（如输入`4`）
2. 进入对应页面查看具体指令
3. 输入相应的编辑指令

### 方法2：直接输入完整指令
在主菜单直接输入完整指令，无需分页：
```
12*999&13*999&14*999&15*999&16*99900&17*999&18*999&19*99900&20*99900&21*99900&22*99900&23*99900&24*99900&25*999&26*999&27*15&28*39&29*999
```

## 📄 各页面详情

### 页面4：掉落物品编辑
```
12*数值 或 无双水晶掉落*数值    # 设置无双水晶掉落数量
13*数值 或 万能球掉落*数值      # 设置万能球掉落数量
14*数值 或 战神之心掉落*数值    # 设置战神之心掉落数量
15*数值 或 幸运值*数值          # 设置幸运值
16*数值 或 商运掉率*数值        # 设置银币获取倍率(%)
17*数值 或 优胜券获取*数值      # 设置优胜券获取数量
18*数值 或 载具碎片掉落*数值    # 设置载具碎片掉落数量

示例: 12*999&13*999&14*999&15*999&16*99900
```

### 页面5：掉率属性编辑
```
19*数值 或 生命催化剂掉率*数值  # 设置生命催化剂掉率(%)
20*数值 或 神能石掉率*数值      # 设置神能石掉率(%)
21*数值 或 转化石掉率*数值      # 设置转化石掉率(%)
22*数值 或 化石掉率*数值        # 设置化石掉率(%)
23*数值 或 血手掉率*数值        # 设置血手掉率(%)
24*数值 或 装置掉率*数值        # 设置装置掉率(%)
32*数值 或 武器碎片掉率*数值    # 设置武器碎片掉率(%)
33*数值 或 装备碎片掉率*数值    # 设置装备碎片掉率(%)
37*数值 或 宝石掉率*数值        # 设置宝石掉率(%)

示例: 19*99900&20*99900&21*99900&22*99900
```

### 页面6：经验好感编辑
```
25*数值 或 赠礼好感度*数值      # 设置赠礼好感度点数
26*数值 或 好感度每天*数值      # 设置每日好感度点数
29*数值 或 每日扫荡次数*数值    # 设置每日扫荡次数
30*数值 或 经验获取*数值        # 设置经验获取倍率(%)
31*数值 或 经验获取VIP*数值     # 设置VIP经验获取倍率(%)

示例: 25*999&26*999&29*999&30*999
```

### 页面7：战斗属性编辑
```
27*数值 或 战斗力神级*数值      # 设置战斗力/神级(%)
28*数值 或 防弹值*数值          # 设置防弹值(%)

示例: 27*15&28*39
```

## 🚀 生成你的超级装备

### 完整指令（一次性设置所有属性）
```
12*999&13*999&14*999&15*999&16*99900&17*999&18*999&19*99900&20*99900&21*99900&22*99900&23*99900&24*99900&25*999&26*999&27*15&28*39&29*999
```

### 分步操作
1. **进入页面4**，输入：`12*999&13*999&14*999&15*999&16*99900&17*999&18*999`
2. **进入页面5**，输入：`19*99900&20*99900&21*99900&22*99900&23*99900&24*99900`
3. **进入页面6**，输入：`25*999&26*999&29*999&30*999`
4. **进入页面7**，输入：`27*15&28*39`

## ✅ 验证结果

编辑成功后会显示：
- 每个属性的设置确认消息
- 最终的"装备属性编辑成功！"消息
- 调试信息显示所有设置的属性

## 🎉 优势

### ✅ 界面友好
- 分页清晰，不再拥挤
- 每页都有详细说明和示例
- 支持中文和数字指令

### ✅ 操作灵活
- 可以分页操作，逐步设置
- 也可以直接输入完整指令
- 完全向后兼容

### ✅ 功能完整
- 支持所有31个扩展属性
- 包含你需要的所有超级属性
- 与基础属性编辑完全兼容

现在你可以轻松生成具有所有你想要属性的超级装备了！
