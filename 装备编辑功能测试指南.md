# 装备编辑功能测试指南

## 🎯 测试目标

验证装备编辑功能的扩展属性（指令12-42）是否正常工作，确保能够成功生成你需要的超级装备。

## 🔧 测试环境

### 支持的编辑方式
1. **右键菜单编辑** - `scripts玉帝后台版/UI/base/btnList/BtnList.as`
2. **装备重铸界面编辑** - `scripts玉帝后台版/UI/forging/equipRemake/EquipRemakeBoard.as`

### 支持的指令范围
- **基础指令**: 00-11（原有功能）
- **扩展指令**: 12-42（新增功能）
- **中文指令**: 支持中文属性名

## 🧪 测试步骤

### 第一步：基础功能测试

#### 测试1：单个属性设置
```
15*999
```
**预期结果**: 设置幸运值999，显示"设置幸运值：999"

#### 测试2：掉率属性测试
```
16*99900
```
**预期结果**: 设置商运掉率99900%，显示"设置商运掉率：99900%"

#### 测试3：中文指令测试
```
幸运值*999
```
**预期结果**: 与数字指令相同效果

### 第二步：组合指令测试

#### 测试4：基础组合
```
12*999&13*999&15*999
```
**预期结果**: 同时设置无双水晶、万能球、幸运值

#### 测试5：掉率组合
```
19*99900&20*99900&21*99900
```
**预期结果**: 同时设置多个掉率属性

### 第三步：完整超级装备测试

#### 测试6：完整指令
```
12*999&13*999&14*999&15*999&16*99900&17*999&18*999&19*99900&20*99900&21*99900&22*99900&23*99900&24*99900&25*999&26*999&27*15&28*39&29*999
```

**预期结果**: 
- 显示多个成功消息
- 最后显示调试信息，包含所有设置的属性

## 📋 验证清单

### ✅ 成功标志
- [ ] 每个指令都显示对应的成功消息
- [ ] 编辑完成后显示"装备属性编辑成功！"
- [ ] 显示调试信息：`装备obj属性: demStroneDropNum=999 demBallDropNum=999...`
- [ ] 装备属性确实被保存到obj对象中

### ❌ 失败标志
- [ ] 指令无响应或报错
- [ ] 成功消息显示但属性未保存
- [ ] 调试信息中缺少某些属性
- [ ] 数值转换错误（如掉率属性）

## 🔍 调试信息解读

### 正确的调试信息示例
```
装备obj属性: demStroneDropNum=999 demBallDropNum=999 madheartDropNum=999 lottery=999 coinMul=999 arenaStampDropNum=999 vehicleCashDropNum=999 lifeCatalystDropPro=999 godStoneDropPro=999 converStoneDropPro=999 taxStampDropPro=999 bloodStoneDropPro=999 deviceDropPro=999 loveAdd=999 dayLoveAdd=999 dpsAll=0.15 bulletDedut=0.39 sweepingNum=999
```

### 数值转换验证
- **掉率属性**: 输入99900 → 存储为999（除以100）
- **百分比属性**: 输入15 → 存储为0.15（除以100）
- **数量属性**: 输入999 → 存储为999（不变）

## 🚨 常见问题排查

### 问题1：指令无效
**症状**: 输入指令后无任何反应
**解决**: 
1. 检查指令格式是否正确（使用*分隔）
2. 确认装备已选中
3. 验证指令代码是否在支持范围内

### 问题2：属性未保存
**症状**: 显示成功但装备属性未改变
**解决**:
1. 检查是否有调试信息显示
2. 确认obj对象是否正确创建
3. 重新查看装备属性

### 问题3：数值异常
**症状**: 保存的数值与输入不符
**解决**:
1. 确认属性类型（掉率vs数量）
2. 检查是否需要除以100转换
3. 验证数值范围是否合理

## 🎮 实际操作流程

### 方法1：右键菜单测试
1. 打开游戏，进入背包界面
2. 右键点击任意装备
3. 选择"装备编辑"选项
4. 输入测试指令
5. 点击确认
6. 查看结果消息

### 方法2：装备重铸界面测试
1. 进入锻造界面
2. 选择"装备" → "装备重铸"
3. 选择要编辑的装备
4. 点击"编辑当前数据"
5. 输入测试指令（可跳过分页菜单）
6. 点击确认
7. 查看结果消息

## 📊 测试记录表

| 测试项 | 指令 | 预期结果 | 实际结果 | 状态 |
|--------|------|----------|----------|------|
| 幸运值 | 15*999 | 设置幸运值999 | | ⏳ |
| 商运掉率 | 16*99900 | 设置商运掉率99900% | | ⏳ |
| 中文指令 | 幸运值*999 | 同数字指令 | | ⏳ |
| 基础组合 | 12*999&13*999&15*999 | 三个属性同时设置 | | ⏳ |
| 完整指令 | [完整指令] | 所有属性设置成功 | | ⏳ |

## 🎉 成功标准

当所有测试项都通过时，说明装备编辑功能已经完全可用，你可以：

1. ✅ 使用完整指令生成超级装备
2. ✅ 自由组合各种属性
3. ✅ 使用中文或数字指令
4. ✅ 在两个界面中都能正常编辑

## 🔄 下一步

测试通过后，你就可以使用《超级装备生成指南.md》中的完整指令来生成你想要的超级装备了！
